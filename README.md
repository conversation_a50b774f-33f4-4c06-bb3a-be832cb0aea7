# Gemini API 密钥扫描器 (深度搜索与多线程版)

这是一个强大、专业且健壮的自动化工具，用于在 GitHub 上深度搜索可能泄露的 Google Gemini API 密钥，并对其进行实时、并发的有效性验证。

## 核心功能 ✨

*   **深度搜索 (Deep Search)**: 通过动态生成和切分查询，智能地绕过 GitHub API 的1000个结果上限，实现远超常规的搜索深度。
*   **流式处理 (Streaming)**: 搜索与验证同时进行，无需等待所有搜索完成，提供实时的进度反馈。
*   **多线程验证 (Multi-threaded)**: 使用线程池并发验证密钥，极大提升处理效率，快速筛选海量结果。
*   **精准断点续传 (Resumable)**: 自动记录每一个独立查询的扫描进度。即使程序意外中断，重启后也能从上次的位置无缝继续，绝不丢失进度。
*   **永久记忆 (Persistent Memory)**: 所有处理过的密钥（无论有效或无效）都会被记录下来，避免重复验证，节约宝贵的API调用次数。
*   **主动速率控制 (Rate Limiting)**: 严格遵守 GitHub API 的速率限制（默认为每分钟10次），确保长时间稳定运行，避免因请求过快被封禁。
*   **优雅停机 (Graceful Shutdown)**: 支持 `Ctrl+C` 安全中断。程序会捕捉中断信号，并确保所有正在进行的任务完成后再干净地退出，不会造成数据损坏。
*   **代理支持 (Proxy Support)**: 可在脚本中轻松配置 HTTP/HTTPS 代理。

## 安装与准备 🛠️

1.  **克隆项目**
    ```bash
    git clone [你的项目仓库地址]
    cd [项目目录]
    ```

2.  **安装依赖**
    项目依赖于几个核心库，通过 `requirements.txt` 文件进行管理。
    ```bash
    pip install -r requirements.txt
    ```

3.  **配置环境变量**
    为了避免 GitHub API 的严格速率限制，你需要使用一个 Personal Access Token (PAT)。

    *   在项目根目录下，创建一个名为 `.env` 的文件。
    *   在 `.env` 文件中，添加以下内容，并替换成你自己的Token：
        ```
        GITHUB_TOKEN=ghp_YourPersonalAccessTokenHere
        ```

## 如何使用 🚀

完成安装和配置后，直接运行主脚本即可：

```bash
python gemini_scanner.py
```

脚本启动后，你将看到实时的日志输出，包括：
*   当前正在处理的搜索查询。
*   正在搜索的页面码。
*   实时验证的密钥状态。
*   最终发现的有效密钥。

你可以随时使用 `Ctrl+C` 来安全地停止扫描。

## 文件结构 📂

*   `gemini_scanner.py`: 主程序脚本。
*   `requirements.txt`: 项目所需的 Python 依赖库。
*   `.env`: (需手动创建) 存放你的 GitHub Token。
*   `README.md`: 本说明文件。

---

*以下文件由脚本自动生成和管理：*

*   `processed_keys.json`: **记忆库**。以 JSON 格式存储所有被处理过的密钥及其状态（`"valid"` 或 `"invalid"`）。
*   `scan_state.json`: **进度书签**。记录每一个深度搜索查询的下一页URL，用于实现断点续传。
*   `valid_keys.txt`: **成果文件**。所有被验证为有效的密钥，都会被追加到这个文件中。

## 高级配置 ⚙️

你可以直接在 `gemini_scanner.py` 脚本的常量区进行一些高级配置：

*   `BASE_QUERIES`: 修改或扩展基础查询列表，以调整搜索的广度和深度。
*   `MAX_WORKERS`: 调整验证线程池的大小，以适应你的机器性能和网络环境（默认为10）。
*   `GITHUB_API_DELAY`: 调整 GitHub API 请求的间隔时间（默认为6秒，即每分钟10次）。
*   `PROXY_URL`: 设置你的 HTTP/HTTPS 代理服务器地址。

---
*这个工具是我们智慧和耐心的结晶，享受它带来的强大能力吧！*