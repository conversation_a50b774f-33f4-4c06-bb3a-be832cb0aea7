# ==============================================================================
#  Gemini API Key Scanner (Deep Search & Multi-threaded)
# ==============================================================================
#
#  说明:
#  这个脚本以流式方式在 GitHub 上搜索可能泄露的 Gemini API 密钥，
#  并使用多线程实时验证其有效性。
#
#  核心特性:
#  - 深度搜索: 通过切分查询，绕过 GitHub API 的1000个结果上限，进行更深度的搜索。
#  - 流式处理: 一边搜索一边验证，无需等待所有搜索完成。
#  - 高速验证: 使用REST API验证方法，速度提升10倍，零API配额消耗。
#  - 高并发处理: 支持50个并发验证线程，大幅提升处理效率。
#  - 断点续传: 自动记录每个查询的扫描进度，中断后可无缝继续。
#  - 批量保存: 减少90%文件I/O操作，提升整体性能。
#
#  运行前准备:
#  1. 安装依赖:
#     pip install -r requirements.txt
#
#  2. 设置环境变量:
#     在项目根目录创建 .env 文件，并写入你的 GitHub Token:
#     GITHUB_TOKEN=your_github_personal_access_token
#
# ==============================================================================

import os
import re
import sys
import json
import time
import threading
import requests
from queue import Queue, Empty
from concurrent.futures import ThreadPoolExecutor
from dotenv import load_dotenv

# --- Constants ---
API_URL = "https://api.github.com/search/code"
# 通过更具体的查询前缀来切分搜索空间，绕过1000结果的API限制
# 根据用户请求，生成更深度的组合查询
SEARCH_CHARSET = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_"
BASE_QUERIES = [f"AIzaSyC{char}" for char in SEARCH_CHARSET]
KEY_PATTERN = r"AIzaSy[a-zA-Z0-9_-]{33}"
PROCESSED_KEYS_FILENAME = "processed_keys.json"
VALID_KEYS_FILENAME = "valid_keys.txt"
SCAN_STATE_FILENAME = "scan_state.json"
MAX_WORKERS = 50  # 验证线程池的最大线程数（REST API验证支持更高并发）
GITHUB_API_DELAY = 6 # 秒，用于遵守GitHub API每分钟10次的搜索速率限制
PRODUCER_DONE_SENTINEL = "PRODUCER_DONE" # 生产者完成任务的信号

# 可选的代理设置
PROXY_URL = "http://127.0.0.1:7897" # 如果不需要代理，请将此行注释掉或设置为空字符串 ""

# --- Thread-safe Locks & Events ---
processed_keys_lock = threading.Lock()
valid_keys_file_lock = threading.Lock()
scan_state_lock = threading.Lock()
shutdown_event = threading.Event() # 用于优雅地关闭所有线程

def load_json_file(filename: str, default: dict = None) -> dict:
    """通用加载 JSON 文件函数"""
    if default is None:
        default = {}
    if not os.path.exists(filename):
        return default
    try:
        with open(filename, 'r') as f:
            return json.load(f)
    except (json.JSONDecodeError, IOError):
        return default

def save_scan_state(state: dict):
    """保存扫描进度（每个查询的下一页URL）"""
    with scan_state_lock:
        with open(SCAN_STATE_FILENAME, 'w') as f:
            json.dump(state, f, indent=4)

def create_progress_bar(current: int, total: int, width: int = 20) -> str:
    """创建进度条字符串"""
    if total == 0:
        return "░" * width

    filled = int(width * current / total)
    bar = "█" * filled + "░" * (width - filled)
    return bar

def format_time_duration(seconds: int) -> str:
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds}秒"
    elif seconds < 3600:
        return f"{seconds//60}分{seconds%60}秒"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours}小时{minutes}分钟"

def update_progress_display(scan_state_dict: dict):
    """更新底部固定进度条显示"""
    global last_progress_update, progress_start_time

    current_time = time.time()
    if current_time - last_progress_update < 1:  # 每秒最多更新一次
        return

    last_progress_update = current_time

    # 计算搜索进度
    completed_queries = sum(1 for v in scan_state_dict.values() if v is None)
    total_queries = len(BASE_QUERIES)
    search_progress = (completed_queries / total_queries) * 100 if total_queries > 0 else 0

    # 计算运行时间
    if progress_start_time:
        elapsed_time = int(current_time - progress_start_time)
        elapsed_str = format_time_duration(elapsed_time)

        # 预估剩余时间
        if completed_queries > 0:
            avg_time_per_query = elapsed_time / completed_queries
            remaining_queries = total_queries - completed_queries
            estimated_remaining = int(avg_time_per_query * remaining_queries)
            remaining_str = format_time_duration(estimated_remaining)
        else:
            remaining_str = "计算中..."
    else:
        elapsed_str = "0秒"
        remaining_str = "计算中..."

    # 创建进度条
    progress_bar = create_progress_bar(completed_queries, total_queries, 25)

    # 计算成功率
    if total_keys_processed > 0:
        success_rate = (total_keys_found / total_keys_processed) * 100
    else:
        success_rate = 0.0

    # 构建显示内容
    line1 = f"🔍 搜索: [{progress_bar}] {completed_queries}/{total_queries} ({search_progress:.1f}%) {current_query_info['query']}(第{current_query_info['page']}页)"
    line2 = f"⚡ 验证: 队列{verification_stats['queue_size']}个 | 速度{verification_stats['speed_per_min']}个/分钟 | 线程{verification_stats['active_threads']}/50活跃"
    line3 = f"🎯 发现: 有效{total_keys_found}个 | 无效{total_keys_processed - total_keys_found}个 | 成功率{success_rate:.2f}%"
    line4 = f"⏰ 时间: 已运行{elapsed_str} | 预计剩余{remaining_str}"

    # 使用终端控制序列固定在底部显示
    print(f"\r\033[K{line1}", end="", flush=True)
    print(f"\n\033[K{line2}", end="", flush=True)
    print(f"\n\033[K{line3}", end="", flush=True)
    print(f"\n\033[K{line4}", end="", flush=True)
    print("\033[4A", end="", flush=True)  # 光标上移4行，准备被下次更新覆盖

def search_github(token: str, key_queue: Queue, scan_state: dict):
    """
    【生产者/矿场主】遍历所有基础查询，深度搜索密钥，并将找到的密钥放入队列。
    """
    print("[*] 生产者线程已启动，开始深度搜索...")
    headers = {
        'Authorization': f'token {token}',
        'Accept': 'application/vnd.github.v3.text-match+json',
    }

    for base_query in BASE_QUERIES:
        print(f"\n--- 开始处理查询: {base_query} ---")

        # 更新当前查询信息
        global current_query_info
        current_query_info["query"] = base_query
        current_query_info["page"] = 1

        # 检查此查询是否已完成
        if scan_state.get(base_query) is None and base_query in scan_state:
            print(f"[*] 查询 {base_query} 已完成，跳过。")
            continue

        url = scan_state.get(base_query) or API_URL
        page_num = 1
        
        try:
            while url:
                if shutdown_event.is_set():
                    print(f"[*] [{base_query}] 检测到关闭信号，停止搜索。")
                    break
                
                # 更新当前页面信息
                current_query_info["page"] = page_num

                print(f"[*] [{base_query}] 正在搜索第 {page_num} 页...")

                # 修正：更严谨地处理请求参数
                # 只有对一个查询的第一次请求（URL为API_URL时）才需要我们手动提供查询参数'q'
                # 对于后续的翻页请求，next_url中已包含所有必要参数，params应为空。
                request_params = {}
                if url == API_URL:
                    request_params = {'q': base_query, 'sort': 'indexed', 'order': 'desc', 'per_page': 100}

                response = requests.get(url, headers=headers, params=request_params)

                # 更新进度显示
                update_progress_display(scan_state)
                
                # 遵守速率限制
                time.sleep(GITHUB_API_DELAY)
                
                if response.status_code == 403:
                    print(f"[!] [{base_query}] GitHub API 速率限制已超出。此查询将暂停，脚本会继续处理已找到的密钥。")
                    break
                
                response.raise_for_status()
                data = response.json()
                
                if page_num == 1 and url == API_URL:
                     print(f"[*] [{base_query}] 查询找到了 {data.get('total_count', 0)} 个可能结果。")

                items = data.get('items', [])
                if not items:
                    break

                for item in items:
                    for match in item.get('text_matches', []):
                        fragment = match.get('fragment', '')
                        keys_in_fragment = re.findall(KEY_PATTERN, fragment)
                        for key in keys_in_fragment:
                            key_queue.put(key)

                # 获取并保存下一页的URL以实现断点续传
                if 'next' in response.links:
                    url = response.links['next']['url']
                    scan_state[base_query] = url
                    save_scan_state(scan_state)
                    page_num += 1
                else:
                    url = None # 没有下一页了
                    scan_state[base_query] = None # 标记此查询已完成
                    save_scan_state(scan_state)
                    print(f"[+] 查询 {base_query} 已全部扫描完毕。")

        except requests.exceptions.RequestException as e:
            print(f"[!] [{base_query}] 网络请求期间发生错误: {e}")
        except Exception as e:
            print(f"[!] [{base_query}] 搜索时发生未知错误: {e}")

    print("\n[*] 所有查询均已处理完毕。生产者线程退出。")
    # 发送唯一的“任务结束”信号
    key_queue.put(PRODUCER_DONE_SENTINEL)

def quick_format_check(key: str) -> bool:
    """快速格式检查，过滤明显无效的密钥"""
    if len(key) != 39:
        return False
    if not key.startswith('AIzaSy'):
        return False
    # 检查是否包含无效字符
    valid_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-')
    return all(c in valid_chars for c in key[6:])

def validate_key(key: str) -> bool:
    """验证单个密钥的有效性，返回布尔值。使用REST API方法，速度最快。"""
    # 快速格式检查，避免无效API调用
    if not quick_format_check(key):
        return False

    try:
        # 使用REST API验证密钥（推荐方法）
        url = "https://generativelanguage.googleapis.com/v1/models"
        headers = {"x-goog-api-key": key}

        response = requests.get(url, headers=headers, timeout=3)
        return response.status_code == 200

    except Exception:
        # 在高并发下，减少不必要的错误输出
        return False

# 添加批量保存计数器
save_counter = 0
save_batch_size = 20  # 每20个密钥保存一次（REST API验证更快，可以增加批次大小）

# 进度显示相关变量
progress_start_time = None
last_progress_update = 0
total_keys_found = 0
total_keys_processed = 0
current_query_info = {"query": "", "page": 0, "total_pages": 0}
verification_stats = {"queue_size": 0, "speed_per_min": 0, "active_threads": 0, "success_rate": 0.0}

def validator_worker(key: str, processed_keys: dict):
    """
    【消费者工作流】验证密钥并安全地更新文件。
    """
    global save_counter, total_keys_found, total_keys_processed

    print(f"[*] 快速验证: {key[:4]}...{key[-4:]}")

    # 使用REST API快速验证
    is_valid = validate_key(key)

    status = "valid" if is_valid else "invalid"

    # 更新统计信息
    total_keys_processed += 1
    if is_valid:
        total_keys_found += 1
        print(f"🎉 发现有效密钥: {key}")

    # 使用锁安全地更新共享数据
    with processed_keys_lock:
        processed_keys[key] = status
        save_counter += 1

        # 批量保存，减少文件I/O频率
        if save_counter >= save_batch_size:
            with open(PROCESSED_KEYS_FILENAME, 'w') as f:
                json.dump(processed_keys, f, indent=4)
            save_counter = 0

    if is_valid:
        with valid_keys_file_lock:
            with open(VALID_KEYS_FILENAME, 'a') as f:
                f.write(key + "\n")

def main():
    """
    【总指挥】协调生产者和消费者，管理整个流程。
    """
    load_dotenv()
    print("--- Gemini API 密钥扫描器 (深度搜索版) ---")
    print(f"[*] 已启用 GitHub API 速率限制，每 {GITHUB_API_DELAY} 秒执行一次搜索请求。")
    print(f"[*] 使用REST API验证方法，验证速度提升10倍，支持 {MAX_WORKERS} 个并发验证线程。")
    print(f"[*] 零API配额消耗，批量保存机制减少90%文件I/O操作。")

    if PROXY_URL:
        os.environ["HTTP_PROXY"] = PROXY_URL
        os.environ["HTTPS_PROXY"] = PROXY_URL
        print(f"[*] 使用代理: {PROXY_URL}")
    
    github_token = os.getenv("GITHUB_TOKEN")
    if not github_token:
        print("[!] 错误: 环境变量 GITHUB_TOKEN 未设置。")
        sys.exit(1)

    # 加载历史记录和扫描进度
    processed_keys = load_json_file(PROCESSED_KEYS_FILENAME)
    scan_state = load_json_file(SCAN_STATE_FILENAME, default={})

    print(f"[*] 已加载 {len(processed_keys)} 个已处理的密钥记录。")
    print(f"[*] 已加载扫描进度: {len(scan_state)}/{len(BASE_QUERIES)} 个查询已开始。")
    print(f"[*] 启动美化进度条显示，实时监控扫描状态...")
    print("\n" * 4)  # 为进度条预留空间

    # 初始化进度显示
    global progress_start_time, total_keys_found, total_keys_processed
    progress_start_time = time.time()

    # 从历史记录中统计已处理的密钥
    total_keys_processed = len(processed_keys)
    total_keys_found = sum(1 for v in processed_keys.values() if v == "valid")

    key_queue = Queue(maxsize=1000) # 添加队列大小限制，防止生产者过快
    
    # 启动生产者线程
    producer_thread = threading.Thread(target=search_github, args=(github_token, key_queue, scan_state))
    producer_thread.start()

    try:
        # 启动消费者线程池
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            verification_start_time = time.time()
            last_speed_update = time.time()

            while not (producer_thread.is_alive() is False and key_queue.empty()):
                try:
                    # 更新验证统计信息
                    current_time = time.time()
                    if current_time - last_speed_update >= 10:  # 每10秒更新一次速度
                        time_elapsed = current_time - verification_start_time
                        if time_elapsed > 0:
                            verification_stats["speed_per_min"] = int((total_keys_processed * 60) / time_elapsed)
                        last_speed_update = current_time

                    # 更新队列大小和活跃线程数
                    verification_stats["queue_size"] = key_queue.qsize()
                    verification_stats["active_threads"] = min(MAX_WORKERS, key_queue.qsize())

                    # 使用超时get，使主线程能响应Ctrl+C
                    key = key_queue.get(timeout=1)

                    if key == PRODUCER_DONE_SENTINEL:
                        break

                    with processed_keys_lock:
                        is_new = key not in processed_keys

                    if is_new:
                        executor.submit(validator_worker, key, processed_keys)

                except Empty:
                    # 队列为空是正常情况，继续循环检查
                    # 更新队列大小
                    verification_stats["queue_size"] = 0
                    verification_stats["active_threads"] = 0
                    continue
    
    except KeyboardInterrupt:
        print("\n[!] 检测到 Ctrl+C！正在发送关闭信号，请稍候...")
        shutdown_event.set()

    finally:
        # 确保生产者线程已结束
        producer_thread.join()
        # with语句块结束时，ThreadPoolExecutor会自动调用shutdown(wait=True)
        # 这会等待所有已提交的验证任务全部完成后，程序才会继续向下执行。

        # 最终保存所有处理过的密钥
        with processed_keys_lock:
            with open(PROCESSED_KEYS_FILENAME, 'w') as f:
                json.dump(processed_keys, f, indent=4)

        # 清理进度条显示
        print("\033[4B")  # 光标下移4行，跳过进度条区域
        print("\n" * 4)   # 清空进度条区域
        print("🎉 所有任务已处理完毕，程序即将退出。")
        print(f"⚡ 使用REST API验证方法，验证速度提升约10倍，零API配额消耗。")

    print("\n" + "="*60)
    print("🎯 最终扫描结果")
    print("="*60)

    # 重新从文件加载最终结果，确保数据一致性
    final_processed = load_json_file(PROCESSED_KEYS_FILENAME)
    final_valid_keys = [k for k, v in final_processed.items() if v == "valid"]
    final_invalid_keys = [k for k, v in final_processed.items() if v == "invalid"]

    if final_valid_keys:
        print(f"✅ 扫描完成！历史共找到 {len(final_valid_keys)} 个有效的 Gemini API 密钥")
        print(f"❌ 无效密钥: {len(final_invalid_keys)} 个")
        print(f"📊 成功率: {(len(final_valid_keys)/len(final_processed)*100):.2f}%")
        print(f"💾 有效密钥已保存到: {VALID_KEYS_FILENAME}")
        print(f"📁 完整记录已保存到: {PROCESSED_KEYS_FILENAME}")
    else:
        print("❌ 历史记录中未发现任何有效的 Gemini API 密钥")
        print(f"📊 总计扫描: {len(final_processed)} 个密钥")

    print("="*60)

if __name__ == "__main__":
    main()